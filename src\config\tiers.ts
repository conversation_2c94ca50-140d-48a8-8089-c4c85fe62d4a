export interface TierConfig {
  id: string;
  name: string;
  order: number;
  color: string;
  bgColor: string;
  textColor: string;
  cssClass: string;
}

// 等级排序映射
export const TIER_ORDER: Record<string, number> = {
  'S+': 14,
  'S': 13,
  'S-': 12,
  'A+': 11,
  'A': 10,
  'A-': 9,
  'B+': 8,
  'B': 7,
  'B-': 6,
  'C+': 5,
  'C': 4,
  'C-': 3,
  'D+': 2,
  'D': 1,
  'D-': 0
};

// 等级配置
export const TIER_CONFIGS: TierConfig[] = [
  {
    id: 'S+',
    name: 'S+',
    order: 9,
    color: 'bg-red-500',
    bgColor: 'bg-red-500',
    textColor: 'text-white',
    cssClass: 'bg-red-500 text-white'
  },
  {
    id: 'S',
    name: 'S',
    order: 8,
    color: 'bg-red-400',
    bgColor: 'bg-red-400',
    textColor: 'text-white',
    cssClass: 'bg-red-500 text-white'
  },
  {
    id: 'A+',
    name: 'A+',
    order: 7,
    color: 'bg-yellow-500',
    bgColor: 'bg-yellow-500',
    textColor: 'text-black',
    cssClass: 'bg-yellow-500 text-black'
  },
  {
    id: 'A',
    name: 'A',
    order: 6,
    color: 'bg-yellow-400',
    bgColor: 'bg-yellow-400',
    textColor: 'text-black',
    cssClass: 'bg-yellow-500 text-black'
  },
  {
    id: 'A-',
    name: 'A-',
    order: 5,
    color: 'bg-yellow-400',
    bgColor: 'bg-yellow-400',
    textColor: 'text-black',
    cssClass: 'bg-yellow-500 text-black'
  },
  {
    id: 'B',
    name: 'B',
    order: 4,
    color: 'bg-green-500',
    bgColor: 'bg-green-500',
    textColor: 'text-white',
    cssClass: 'bg-green-500 text-white'
  },
  {
    id: 'C',
    name: 'C',
    order: 3,
    color: 'bg-blue-500',
    bgColor: 'bg-blue-500',
    textColor: 'text-white',
    cssClass: 'bg-blue-500 text-white'
  },
  {
    id: 'D',
    name: 'D',
    order: 2,
    color: 'bg-gray-500',
    bgColor: 'bg-gray-500',
    textColor: 'text-white',
    cssClass: 'bg-gray-500 text-white'
  },
  {
    id: 'D-',
    name: 'D-',
    order: 1,
    color: 'bg-gray-400',
    bgColor: 'bg-gray-400',
    textColor: 'text-white',
    cssClass: 'bg-gray-500 text-white'
  }
];

// 等级ID数组
export const TIER_IDS = TIER_CONFIGS.map(tier => tier.id);

// 根据ID获取等级配置
export const getTierById = (id: string): TierConfig | undefined => {
  return TIER_CONFIGS.find(tier => tier.id === id);
};

// 获取等级CSS类
export const getTierClass = (tier: string): string => {
  const tierConfig = getTierById(tier.toUpperCase());
  return tierConfig ? tierConfig.cssClass : 'bg-gray-500 text-white';
};

// 获取等级颜色映射（用于图表）
export const getTierColors = (): Record<string, string> => {
  return TIER_CONFIGS.reduce((acc, tier) => {
    acc[tier.id] = tier.color;
    return acc;
  }, {} as Record<string, string>);
};

// 获取等级选项（用于下拉框等）
export const getTierOptions = (includeAll: boolean = false) => {
  const options = TIER_CONFIGS
    .sort((a, b) => b.order - a.order) // 按order降序排列
    .map(tier => ({
      value: tier.id,
      label: tier.name,
      id: tier.id
    }));
  
  if (includeAll) {
    return [{ value: '', label: '全部段位', id: '' }, ...options];
  }
  
  return options;
};

// 排序玩家函数
export const sortPlayersByTier = <T extends { tier: string; user_ranks?: any[] }>(players: T[]): T[] => {
  return players.sort((a, b) => {
    // 首先按照Tier排序
    const tierDiff = (TIER_ORDER[b.tier as keyof typeof TIER_ORDER] || 0) -
                    (TIER_ORDER[a.tier as keyof typeof TIER_ORDER] || 0);

    // 如果Tier不同，直接返回Tier的排序结果
    if (tierDiff !== 0) {
      return tierDiff;
    }

    // 如果Tier相同，按照Rank排序（如果有的话）
    if (a.user_ranks && b.user_ranks) {
      const aHasRank = a.user_ranks.some(ur => ur.rank);
      const bHasRank = b.user_ranks.some(ur => ur.rank);
      
      if (aHasRank && !bHasRank) return -1;
      if (!aHasRank && bHasRank) return 1;
      
      if (aHasRank && bHasRank) {
        const aHighestPriority = Math.max(...a.user_ranks.filter(ur => ur.rank).map(ur => ur.rank.priority));
        const bHighestPriority = Math.max(...b.user_ranks.filter(ur => ur.rank).map(ur => ur.rank.priority));
        return bHighestPriority - aHighestPriority;
      }
    }

    return 0;
  });
};
